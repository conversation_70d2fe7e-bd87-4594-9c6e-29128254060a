"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/store-result/route";
exports.ids = ["app/api/store-result/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstore-result%2Froute&page=%2Fapi%2Fstore-result%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstore-result%2Froute.ts&appDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstore-result%2Froute&page=%2Fapi%2Fstore-result%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstore-result%2Froute.ts&appDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Sarth_Downloads_typing_G_TypeArena_src_app_api_store_result_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/store-result/route.ts */ \"(rsc)/./src/app/api/store-result/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/store-result/route\",\n        pathname: \"/api/store-result\",\n        filename: \"route\",\n        bundlePath: \"app/api/store-result/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\api\\\\store-result\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Sarth_Downloads_typing_G_TypeArena_src_app_api_store_result_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/store-result/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstore-result%2Froute&page=%2Fapi%2Fstore-result%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstore-result%2Froute.ts&appDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/store-result/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/store-result/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _dbconfig_dbconfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/dbconfig/dbconfig */ \"(rsc)/./src/dbconfig/dbconfig.ts\");\n/* harmony import */ var _lib_getDataFromToken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/getDataFromToken */ \"(rsc)/./src/lib/getDataFromToken.ts\");\n/* harmony import */ var _models_user_model__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/user.model */ \"(rsc)/./src/models/user.model.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n\n\n(0,_dbconfig_dbconfig__WEBPACK_IMPORTED_MODULE_0__.connectDb)();\nasync function PUT(request) {\n    try {\n        const reqBody = await request.json();\n        const { speed, accuracy } = reqBody;\n        // Verify user token\n        const reqUserId = (0,_lib_getDataFromToken__WEBPACK_IMPORTED_MODULE_1__.getDataFromToken)(request);\n        if (!reqUserId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n                message: \"User not logged in\"\n            }, {\n                status: 401\n            });\n        }\n        // Validate accuracy\n        if (accuracy < 40) {\n            return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n                message: \"Accuracy too low\"\n            }, {\n                status: 403\n            });\n        }\n        // Fetch user data\n        const user = await _models_user_model__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findById(reqUserId);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n                message: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Default to 0 if fields are undefined\n        const currentAvgSpeed = user.avgSpeed || 0;\n        const currentTestAttempted = user.testAttempted || 0;\n        const currentTopSpeed = user.topSpeed || 0;\n        const currentAccuracy = user.accuracy || 0;\n        // Calculate new metrics\n        const totalSpeed = currentAvgSpeed * currentTestAttempted + speed;\n        const newTestAttempted = currentTestAttempted + 1;\n        const newAvgSpeed = Math.round(totalSpeed / newTestAttempted);\n        const newTopSpeed = Math.max(currentTopSpeed, speed);\n        const newAccuracy = Math.max(currentAccuracy, accuracy);\n        // Add new history entry\n        const historyEntry = {\n            speed,\n            accuracy,\n            testPlayed: new Date()\n        };\n        user.history.push(historyEntry);\n        // Update user\n        user.testAttempted = newTestAttempted;\n        user.avgSpeed = newAvgSpeed;\n        user.topSpeed = newTopSpeed;\n        user.accuracy = newAccuracy;\n        user.lastActive = new Date();\n        // Save changes\n        await user.save();\n        return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n            message: \"Result stored successfully\"\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n            message: \"Failed to update the result\",\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/store-result/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/dbconfig/dbconfig.ts":
/*!**********************************!*\
  !*** ./src/dbconfig/dbconfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connectDb: () => (/* binding */ connectDb)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function connectDb() {\n    try {\n        if (!process.env.MONGODB_URL) {\n            throw new Error(\"MONGO_URL is not defined in the environment variables\");\n        }\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(process.env.MONGODB_URL);\n        const connection = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection);\n        connection.on(\"connected\", ()=>{\n            console.log(\"Connected to MongoDB\");\n        });\n        connection.on(\"error\", (error)=>{\n            console.log(\"Error connecting to MongoDB\");\n            console.error(error);\n            process.exit(1);\n        });\n    } catch (error) {\n        console.log(\"Something went wrong while connecting to DB\");\n        console.error(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/dbconfig/dbconfig.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/getDataFromToken.ts":
/*!*************************************!*\
  !*** ./src/lib/getDataFromToken.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDataFromToken: () => (/* binding */ getDataFromToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\nconst getDataFromToken = (request)=>{\n    try {\n        const token = request.cookies.get(\"token\")?.value || \"\";\n        const decodeToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, process.env.TOKEN_SECRET);\n        return decodeToken.id;\n    } catch (error) {\n        console.log(error.message);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dldERhdGFGcm9tVG9rZW4udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQzhCO0FBR3ZCLE1BQU1DLG1CQUFtQixDQUFDQztJQUM3QixJQUFJO1FBQ0EsTUFBTUMsUUFBUUQsUUFBUUUsT0FBTyxDQUFDQyxHQUFHLENBQUMsVUFBVUMsU0FBUztRQUNyRCxNQUFNQyxjQUFrQlAsMERBQVUsQ0FBQ0csT0FBTU0sUUFBUUMsR0FBRyxDQUFDQyxZQUFZO1FBRWpFLE9BQU9KLFlBQVlLLEVBQUU7SUFFekIsRUFBRSxPQUFPQyxPQUFXO1FBRWhCQyxRQUFRQyxHQUFHLENBQUNGLE1BQU1HLE9BQU87SUFDN0I7QUFDSixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHlwZWFyZW5hLy4vc3JjL2xpYi9nZXREYXRhRnJvbVRva2VuLnRzPzE3YTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QgfSBmcm9tIFwibmV4dC9zZXJ2ZXJcIjtcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJ1xuXG5cbmV4cG9ydCBjb25zdCBnZXREYXRhRnJvbVRva2VuID0gKHJlcXVlc3Q6TmV4dFJlcXVlc3QpID0+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCB0b2tlbiA9IHJlcXVlc3QuY29va2llcy5nZXQoXCJ0b2tlblwiKT8udmFsdWUgfHwgXCJcIjtcbiAgICAgICAgY29uc3QgZGVjb2RlVG9rZW46YW55ID0gand0LnZlcmlmeSh0b2tlbixwcm9jZXNzLmVudi5UT0tFTl9TRUNSRVQhKTtcbiAgICAgICAgXG4gICAgICAgIHJldHVybiBkZWNvZGVUb2tlbi5pZFxuXG4gICAgfSBjYXRjaCAoZXJyb3I6YW55KSB7XG4gICAgXG4gICAgICAgIGNvbnNvbGUubG9nKGVycm9yLm1lc3NhZ2UpO1xuICAgIH1cbn0iXSwibmFtZXMiOlsiand0IiwiZ2V0RGF0YUZyb21Ub2tlbiIsInJlcXVlc3QiLCJ0b2tlbiIsImNvb2tpZXMiLCJnZXQiLCJ2YWx1ZSIsImRlY29kZVRva2VuIiwidmVyaWZ5IiwicHJvY2VzcyIsImVudiIsIlRPS0VOX1NFQ1JFVCIsImlkIiwiZXJyb3IiLCJjb25zb2xlIiwibG9nIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/getDataFromToken.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/user.model.ts":
/*!**********************************!*\
  !*** ./src/models/user.model.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst userSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    fullname: {\n        type: String,\n        required: true,\n        trim: true,\n        minlength: 3,\n        maxlength: 50,\n        lowercase: true\n    },\n    email: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true,\n        email: true\n    },\n    password: {\n        type: String,\n        required: true,\n        minlength: 5,\n        maxlength: 2000\n    },\n    username: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true,\n        minlength: 3,\n        maxlength: 20\n    },\n    bio: {\n        type: String,\n        default: \"\"\n    },\n    testAttempted: {\n        type: Number,\n        default: 0\n    },\n    topSpeed: {\n        type: Number,\n        default: 0\n    },\n    avgSpeed: {\n        type: Number,\n        default: 0\n    },\n    achievement: {\n        type: [\n            String\n        ]\n    },\n    profilePicUrl: {\n        type: String,\n        default: \"\"\n    },\n    lastActive: {\n        type: Date,\n        default: Date.now\n    },\n    history: {\n        type: [\n            {\n                speed: {\n                    type: Number\n                },\n                accuracy: {\n                    type: Number\n                },\n                testPlayed: {\n                    type: Date,\n                    default: Date.now\n                }\n            }\n        ],\n        default: []\n    }\n});\nconst User = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", userSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (User);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/user.model.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/safe-buffer","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstore-result%2Froute&page=%2Fapi%2Fstore-result%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstore-result%2Froute.ts&appDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();