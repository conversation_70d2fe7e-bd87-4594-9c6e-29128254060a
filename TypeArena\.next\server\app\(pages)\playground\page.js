/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(pages)/playground/page";
exports.ids = ["app/(pages)/playground/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(pages)%2Fplayground%2Fpage&page=%2F(pages)%2Fplayground%2Fpage&appPaths=%2F(pages)%2Fplayground%2Fpage&pagePath=private-next-app-dir%2F(pages)%2Fplayground%2Fpage.tsx&appDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(pages)%2Fplayground%2Fpage&page=%2F(pages)%2Fplayground%2Fpage&appPaths=%2F(pages)%2Fplayground%2Fpage&pagePath=private-next-app-dir%2F(pages)%2Fplayground%2Fpage.tsx&appDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(pages)',\n        {\n        children: [\n        'playground',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(pages)/playground/page.tsx */ \"(rsc)/./src/app/(pages)/playground/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(pages)/playground/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(pages)/playground/page\",\n        pathname: \"/playground\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkYocGFnZXMpJTJGcGxheWdyb3VuZCUyRnBhZ2UmcGFnZT0lMkYocGFnZXMpJTJGcGxheWdyb3VuZCUyRnBhZ2UmYXBwUGF0aHM9JTJGKHBhZ2VzKSUyRnBsYXlncm91bmQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGKHBhZ2VzKSUyRnBsYXlncm91bmQlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDU2FydGglNUNEb3dubG9hZHMlNUN0eXBpbmclMjBHJTVDVHlwZUFyZW5hJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNTYXJ0aCU1Q0Rvd25sb2FkcyU1Q3R5cGluZyUyMEclNUNUeXBlQXJlbmEmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLHNMQUE4SDtBQUNySjtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLDRCQUE0QiwwTkFBZ0Y7QUFDNUc7QUFDQSxvQ0FBb0Msc2ZBQW1RO0FBQ3ZTO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUEyRztBQUNwSSxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0Esb0NBQW9DLHNmQUFtUTtBQUN2UztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHlwZWFyZW5hLz9hMGJkIl0sInNvdXJjZXNDb250ZW50IjpbIlwiVFVSQk9QQUNLIHsgdHJhbnNpdGlvbjogbmV4dC1zc3IgfVwiO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJyhwYWdlcyknLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdwbGF5Z3JvdW5kJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU2FydGhcXFxcRG93bmxvYWRzXFxcXHR5cGluZyBHXFxcXFR5cGVBcmVuYVxcXFxzcmNcXFxcYXBwXFxcXChwYWdlcylcXFxccGxheWdyb3VuZFxcXFxwYWdlLnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxTYXJ0aFxcXFxEb3dubG9hZHNcXFxcdHlwaW5nIEdcXFxcVHlwZUFyZW5hXFxcXHNyY1xcXFxhcHBcXFxcKHBhZ2VzKVxcXFxwbGF5Z3JvdW5kXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxTYXJ0aFxcXFxEb3dubG9hZHNcXFxcdHlwaW5nIEdcXFxcVHlwZUFyZW5hXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTYXJ0aFxcXFxEb3dubG9hZHNcXFxcdHlwaW5nIEdcXFxcVHlwZUFyZW5hXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxTYXJ0aFxcXFxEb3dubG9hZHNcXFxcdHlwaW5nIEdcXFxcVHlwZUFyZW5hXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxTYXJ0aFxcXFxEb3dubG9hZHNcXFxcdHlwaW5nIEdcXFxcVHlwZUFyZW5hXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxTYXJ0aFxcXFxEb3dubG9hZHNcXFxcdHlwaW5nIEdcXFxcVHlwZUFyZW5hXFxcXHNyY1xcXFxhcHBcXFxcKHBhZ2VzKVxcXFxwbGF5Z3JvdW5kXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvKHBhZ2VzKS9wbGF5Z3JvdW5kL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvKHBhZ2VzKS9wbGF5Z3JvdW5kL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL3BsYXlncm91bmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(pages)%2Fplayground%2Fpage&page=%2F(pages)%2Fplayground%2Fpage&appPaths=%2F(pages)%2Fplayground%2Fpage&pagePath=private-next-app-dir%2F(pages)%2Fplayground%2Fpage.tsx&appDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Ccomponents%5C%5Ccustom%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-providers.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Ccomponents%5C%5Ccustom%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-providers.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/analytics/dist/react/index.mjs */ \"(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/custom/Navbar.tsx */ \"(ssr)/./src/components/custom/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-providers.tsx */ \"(ssr)/./src/components/theme-providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Ccomponents%5C%5Ccustom%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-providers.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Capp%5C%5C(pages)%5C%5Cplayground%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Capp%5C%5C(pages)%5C%5Cplayground%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(pages)/playground/page.tsx */ \"(ssr)/./src/app/(pages)/playground/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NhcnRoJTVDJTVDRG93bmxvYWRzJTVDJTVDdHlwaW5nJTIwRyU1QyU1Q1R5cGVBcmVuYSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1QyhwYWdlcyklNUMlNUNwbGF5Z3JvdW5kJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUE4SCIsInNvdXJjZXMiOlsid2VicGFjazovL3R5cGVhcmVuYS8/MmI2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhcnRoXFxcXERvd25sb2Fkc1xcXFx0eXBpbmcgR1xcXFxUeXBlQXJlbmFcXFxcc3JjXFxcXGFwcFxcXFwocGFnZXMpXFxcXHBsYXlncm91bmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSarth%5C%5CDownloads%5C%5Ctyping%20G%5C%5CTypeArena%5C%5Csrc%5C%5Capp%5C%5C(pages)%5C%5Cplayground%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(pages)/playground/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/(pages)/playground/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TypingTest)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _data_sampleParagraphs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/sampleParagraphs */ \"(ssr)/./src/data/sampleParagraphs.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst TIME_LIMIT = 60;\nfunction TypingTest() {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(TIME_LIMIT);\n    const [isTestRunning, setIsTestRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // Changed to false initially\n    ;\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // New state to track if test has started\n    ;\n    const [typedText, setTypedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [accuracy, setAccuracy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [wpm, setWpm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Select random sample text on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const selectRandomSampleText = ()=>{\n            const randomIndex = Math.floor(Math.random() * _data_sampleParagraphs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].paragraphs.length);\n            return _data_sampleParagraphs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].paragraphs[randomIndex].text;\n        };\n        setText(selectRandomSampleText());\n        if (textAreaRef.current) {\n            textAreaRef.current.focus();\n        }\n    }, []);\n    // Timer logic\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isTestRunning && timeLeft > 0) {\n            const timer = setTimeout(()=>setTimeLeft(timeLeft - 1), 1000);\n            return ()=>clearTimeout(timer);\n        } else if (timeLeft === 0 && isTestRunning) {\n            setIsTestRunning(false);\n            setIsDialogOpen(true);\n        }\n    }, [\n        isTestRunning,\n        timeLeft\n    ]);\n    // Typing handler\n    const handleTyping = (e)=>{\n        const typed = e.target.value;\n        if (typed.length > text.length) return; // Prevent typing beyond the text length\n        // Start the test on first keystroke\n        if (!hasStarted && typed.length > 0) {\n            setHasStarted(true);\n            setIsTestRunning(true);\n        }\n        setTypedText(typed);\n        const charactersTyped = typed.length;\n        const correctChars = text.substring(0, charactersTyped);\n        let correctCount = 0;\n        for(let i = 0; i < charactersTyped; i++){\n            if (typed[i] === correctChars[i]) {\n                correctCount++;\n            }\n        }\n        const accuracyValue = charactersTyped > 0 ? correctCount / charactersTyped * 100 : 100;\n        setAccuracy(accuracyValue);\n        const words = typed.trim().split(/\\s+/).filter((word)=>word.length > 0);\n        const wpmValue = timeLeft < TIME_LIMIT ? words.length / ((TIME_LIMIT - timeLeft) / 60) : 0;\n        setWpm(Math.round(wpmValue));\n        if (typed === text) {\n            // Stop the test if text is completed\n            setIsTestRunning(false);\n            setIsDialogOpen(true);\n        }\n    };\n    // Store results when dialog is displayed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isTestRunning && hasStarted && typedText.length > 0) {\n            const storeResult = async ()=>{\n                try {\n                    const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].put(\"/api/store-result\", {\n                        speed: wpm,\n                        accuracy\n                    });\n                    console.log(\"Result stored:\", response.data);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Result stored successfully\");\n                } catch (error) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to store result\");\n                    console.log(\"Error while storing the result\");\n                }\n            };\n            storeResult();\n        }\n    }, [\n        isTestRunning,\n        hasStarted\n    ]);\n    // Reset function\n    const handleReset = ()=>{\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container min-h-screen mx-auto px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: [\n                        !isDialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-lg font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-3xl\",\n                                                children: hasStarted ? `${timeLeft}s` : \"Start typing to begin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative min-h-[500px] w-full rounded-lg bg-background p-4 font-mono text-xl md:text-3xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 p-4 pointer-events-none whitespace-pre-wrap break-words leading-relaxed tracking-wide\",\n                                            style: {\n                                                wordSpacing: \"0.25em\"\n                                            },\n                                            \"aria-hidden\": \"true\",\n                                            children: text.split(\"\").map((char, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: index < typedText.length ? typedText[index] === char ? \"text-green-500 dark:text-gray-200\" : \"text-red-500\" : \"text-gray-500\",\n                                                    children: char\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: textAreaRef,\n                                            value: typedText,\n                                            onChange: handleTyping,\n                                            className: \"relative min-h-[700px] md:min-h-[500px] w-full text-transparent caret-black dark:caret-white resize-none bg-transparent p-0 font-inherit leading-relaxed tracking-wide focus:outline-none focus:ring-0\",\n                                            style: {\n                                                wordSpacing: \"0.25em\"\n                                            },\n                                            placeholder: \"\",\n                                            disabled: !isTestRunning && hasStarted,\n                                            onPaste: (e)=>e.preventDefault(),\n                                            onContextMenu: (e)=>e.preventDefault()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                            open: isDialogOpen,\n                            onOpenChange: setIsDialogOpen,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                                children: \"Typing Test Results\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                                children: \"Here's how you performed:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl mb-2\",\n                                                children: [\n                                                    \"Your WPM: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold\",\n                                                        children: wpm\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl\",\n                                                children: [\n                                                    \"Your Accuracy: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold\",\n                                                        children: [\n                                                            accuracy.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 34\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleReset,\n                                            className: \"bg-gray-700 hover:bg-gray-900 text-white\",\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                position: \"bottom-right\",\n                reverseOrder: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\(pages)\\\\playground\\\\page.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(pages)/playground/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/custom/ModeToggle.tsx":
/*!**********************************************!*\
  !*** ./src/components/custom/ModeToggle.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModeToggle: () => (/* binding */ ModeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ ModeToggle auto */ \n\n\n\n\n\nfunction ModeToggle() {\n    const { setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"outline\",\n                    size: \"icon\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: \"Toggle theme\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                align: \"end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                        onClick: ()=>setTheme(\"light\"),\n                        children: \"Light\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                        onClick: ()=>setTheme(\"dark\"),\n                        children: \"Dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                        onClick: ()=>setTheme(\"system\"),\n                        children: \"System\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\ModeToggle.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/custom/ModeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/custom/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/custom/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sheet */ \"(ssr)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Menu!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Menu!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/useStore */ \"(ssr)/./src/store/useStore.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _ModeToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ModeToggle */ \"(ssr)/./src/components/custom/ModeToggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\n\n\n\n\n\n\nconst NavItems = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/playground\",\n                className: \"text-gray-600 dark:text-gray-200 hover:text-gray-900 transition-colors\",\n                children: \"Play Now\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                lineNumber: 15,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/lobby\",\n                className: \"text-gray-600 dark:text-gray-200 hover:text-gray-900 transition-colors\",\n                children: \"Play with friends\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                lineNumber: 18,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/subscription\",\n                className: \"text-gray-600 dark:text-gray-200 hover:text-gray-900 transition-colors\",\n                children: \"Pricing\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/leaderboard\",\n                className: \"text-gray-600 dark:text-gray-200 hover:text-gray-900 transition-colors\",\n                children: \"leaderboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                lineNumber: 24,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModeToggle__WEBPACK_IMPORTED_MODULE_7__.ModeToggle, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                lineNumber: 27,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true);\nfunction Navbar() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { isAuthenticated, logout, user } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleLogout = async ()=>{\n        // api call for logout \n        const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/api/logout\");\n        console.log(\"response:\", response.data);\n        // Call the logout function from your auth store\n        logout();\n        router.push(\"/login\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"mx-auto dark:bg-background p-6 border-b  w-full \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"flex justify-between items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl dark:text-black font-bold text-gray-900\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/\",\n                        className: \"text-2xl font-semibold text-yellow-500\",\n                        children: \"TypeArena\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:flex justify-center items-center space-x-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItems, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            className: \" bg-yellow-500 text-white\",\n                            onClick: handleLogout,\n                            children: \"Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/signup\",\n                            className: \"text-sm bg-yellow-500 text-white px-4 py-2 rounded-full hover:bg-yellow-600 transition-colors flex items-center\",\n                            children: [\n                                \"Join Now\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"ml-1 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.Sheet, {\n                    open: isOpen,\n                    onOpenChange: setIsOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetTrigger, {\n                            asChild: true,\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                \"aria-label\": \"Menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetContent, {\n                            side: \"right\",\n                            className: \"w-[240px] sm:w-[300px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-col space-y-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    user?.token ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"bg-yellow-500 text-white\",\n                                        onClick: handleLogout,\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/signup\",\n                                        children: \"Join Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Navbar.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/custom/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/theme-providers.tsx":
/*!********************************************!*\
  !*** ./src/components/theme-providers.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\theme-providers.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy90aGVtZS1wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFFMUQsU0FBU0MsY0FBYyxFQUM1QkUsUUFBUSxFQUNSLEdBQUdDLE9BQzZDO0lBQ2hELHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL3R5cGVhcmVuYS8uL3NyYy9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVycy50c3g/NGRjYSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7XG4gIGNoaWxkcmVuLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIE5leHRUaGVtZXNQcm92aWRlcj4pIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/theme-providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-yellow-500-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-yellow-500 underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground \", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 48,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogTrigger,DialogClose,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 87,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 102,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dropdown-menu.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/dropdown-menu.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.ChevronRightIcon, {\n                className: \"ml-auto\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\", \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.CheckIcon, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.DotFilledIcon, {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/sheet.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/sheet.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sheet: () => (/* binding */ Sheet),\n/* harmony export */   SheetClose: () => (/* binding */ SheetClose),\n/* harmony export */   SheetContent: () => (/* binding */ SheetContent),\n/* harmony export */   SheetDescription: () => (/* binding */ SheetDescription),\n/* harmony export */   SheetFooter: () => (/* binding */ SheetFooter),\n/* harmony export */   SheetHeader: () => (/* binding */ SheetHeader),\n/* harmony export */   SheetOverlay: () => (/* binding */ SheetOverlay),\n/* harmony export */   SheetPortal: () => (/* binding */ SheetPortal),\n/* harmony export */   SheetTitle: () => (/* binding */ SheetTitle),\n/* harmony export */   SheetTrigger: () => (/* binding */ SheetTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Sheet,SheetPortal,SheetOverlay,SheetTrigger,SheetClose,SheetContent,SheetHeader,SheetFooter,SheetTitle,SheetDescription auto */ \n\n\n\n\n\nconst Sheet = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Root;\nconst SheetTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Trigger;\nconst SheetClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close;\nconst SheetPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Portal;\nconst SheetOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props,\n        ref: ref\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 22,\n        columnNumber: 3\n    }, undefined));\nSheetOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay.displayName;\nconst sheetVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out\", {\n    variants: {\n        side: {\n            top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n            bottom: \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n            left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n            right: \"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\"\n        }\n    },\n    defaultVariants: {\n        side: \"right\"\n    }\n});\nconst SheetContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ side = \"right\", className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetOverlay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                lineNumber: 61,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(sheetVariants({\n                    side\n                }), className),\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_5__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 7\n                    }, undefined),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                lineNumber: 62,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nSheetContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\nconst SheetHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-2 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined);\nSheetHeader.displayName = \"SheetHeader\";\nconst SheetFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined);\nSheetFooter.displayName = \"SheetFooter\";\nconst SheetTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-lg font-semibold text-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 109,\n        columnNumber: 3\n    }, undefined));\nSheetTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst SheetDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 121,\n        columnNumber: 3\n    }, undefined));\nSheetDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/sheet.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/sampleParagraphs.ts":
/*!**************************************!*\
  !*** ./src/data/sampleParagraphs.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst sampleParagraphs = {\n    \"paragraphs\": [\n        {\n            \"id\": 1,\n            \"text\": \"the beach is a calm place to rest waves are soft the sand is warm and the wind is cool people love to swim build in sand or just sit and enjoy the view the sunsets are bright and colorful and sometimes you may even see dolphins swimming in the sea\"\n        },\n        {\n            \"id\": 2,\n            \"text\": \"a forest is quiet and full of life birds sing and the trees move with the wind sunlight comes through the trees making the ground glow you can walk on soft paths and hear the sounds of nature you might even see small animals like rabbits or deer hiding nearby\"\n        },\n        {\n            \"id\": 3,\n            \"text\": \"mountains are big and full of beauty people hike or climb them to enjoy fresh air and amazing views in spring flowers bloom and cover the hills in winter they are covered in snow making them perfect for skiing the top of a mountain shows how wide and vast the world is\"\n        },\n        {\n            \"id\": 4,\n            \"text\": \"cities are full of people and fun things to do there are tall homes parks and busy streets shops are everywhere and you can always find new foods to try at night the lights make the city bright and alive some parks offer quiet spots to rest from the busy day\"\n        },\n        {\n            \"id\": 5,\n            \"text\": \"the ocean is huge and has so many fish and plants people love to swim sail or even dive under the water to see life dolphins turtles and colorful fish live there we need to keep the water clean and safe so the ocean can stay full of life for years to come\"\n        },\n        {\n            \"id\": 6,\n            \"text\": \"phones and computers help us stay close to others even far away they make work and school easy to manage but they can also be hard to handle when used too much it is good to take breaks go outside and talk to people face to face when possible\"\n        },\n        {\n            \"id\": 7,\n            \"text\": \"saving the earth means using less and helping more we can plant trees save water and use less power solar energy from the sun and windmills can make clean power if we all work together we can make the earth a better home for animals plants and people\"\n        },\n        {\n            \"id\": 8,\n            \"text\": \"ai helps people work faster and smarter it can read big data and give quick answers doctors use it to find health problems and stores use it to help people shop but ai must be fair and people need to guide it to make sure it does good things\"\n        },\n        {\n            \"id\": 9,\n            \"text\": \"the brain helps us think and feel in smart ways it works with the body to move learn and grow people study the brain to help solve sadness and stress with new ideas we can find better ways to stay happy and healthy while making smarter choices in life\"\n        },\n        {\n            \"id\": 10,\n            \"text\": \"the weather is changing fast and this is not good for animals or people ice is melting and some animals are losing their homes we need to use clean power save trees and drive less to help small actions like these can make a big change over time\"\n        },\n        {\n            \"id\": 11,\n            \"text\": \"gardens are full of colors with flowers fruits and trees birds and bees visit to help plants grow people enjoy sitting in gardens to relax or read planting a garden at home can be fun and gives fresh fruits and veggies to eat it also helps keep the air clean\"\n        },\n        {\n            \"id\": 12,\n            \"text\": \"rivers flow with fresh water for plants animals and people people swim fish and sail on rivers they bring life to the land around them keeping rivers clean is important for the health of all living things trash and waste must be kept far from these precious waters\"\n        },\n        {\n            \"id\": 13,\n            \"text\": \"rainbows are a magical sight after rain they appear when sunlight hits water drops in the air kids and adults love looking for all the colors in a rainbow they remind us how beautiful nature is seeing one can make any rainy day feel bright and cheerful again\"\n        },\n        {\n            \"id\": 14,\n            \"text\": \"books open up a world of stories and knowledge they let us travel to new places and meet new people in our minds reading helps us learn new words and ideas you can read about animals space or fun adventures books are like doors to exciting new worlds\"\n        },\n        {\n            \"id\": 15,\n            \"text\": \"sports are fun and help keep the body strong people love to play soccer run or swim playing in teams teaches sharing and working together sports also make us happy and give us energy everyone can find a sport they enjoy and it is a great way to make friends\"\n        },\n        {\n            \"id\": 16,\n            \"text\": \"clouds in the sky are soft and white floating high above us sometimes they are gray and bring rain watching clouds is calming and they can look like animals or shapes when the sun sets clouds turn orange and pink making the sky look like a big painting\"\n        },\n        {\n            \"id\": 17,\n            \"text\": \"the desert is dry and sandy but it is full of life cacti store water and animals like lizards live there deserts are very hot in the day and cool at night people visit to see the wide open land and the clear stars shining bright in the dark sky\"\n        },\n        {\n            \"id\": 18,\n            \"text\": \"farms are where food is grown farmers work hard to plant and care for crops there are animals like cows hens and sheep that give us milk eggs and wool visiting a farm teaches how food reaches our tables and shows how much care is needed for it\"\n        },\n        {\n            \"id\": 19,\n            \"text\": \"the zoo is a fun place to see animals from around the world lions elephants and penguins live there zoos teach us about animals and how to care for them they also help save animals that are in danger kids and adults love to visit and learn at zoos\"\n        },\n        {\n            \"id\": 20,\n            \"text\": \"the sky at night is full of stars and wonders people use telescopes to see planets and moons the moon glows bright and shooting stars make wishes fun stargazing is peaceful and helps us feel small but part of a big beautiful universe filled with mysteries to explore\"\n        }\n    ]\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sampleParagraphs);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/sampleParagraphs.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3R5cGVhcmVuYS8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/useStore.ts":
/*!*******************************!*\
  !*** ./src/store/useStore.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// Create the zustand store with type annotations and persist middleware\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set)=>({\n        isAuthenticated: false,\n        user: null,\n        login: (userData)=>set({\n                isAuthenticated: true,\n                user: userData\n            }),\n        logout: ()=>set({\n                isAuthenticated: false,\n                user: null\n            })\n    }), {\n    name: \"auth-storage\"\n}));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAuthStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RvcmUvdXNlU3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlDO0FBQ1k7QUFtQjdDLHdFQUF3RTtBQUN4RSxNQUFNRSxlQUFlRiwrQ0FBTUEsQ0FDekJDLDJEQUFPQSxDQUNMLENBQUNFLE1BQVM7UUFDUkMsaUJBQWlCO1FBQ2pCQyxNQUFNO1FBQ05DLE9BQU8sQ0FBQ0MsV0FBbUJKLElBQUk7Z0JBQUVDLGlCQUFpQjtnQkFBTUMsTUFBTUU7WUFBUztRQUN2RUMsUUFBUSxJQUFNTCxJQUFJO2dCQUFFQyxpQkFBaUI7Z0JBQU9DLE1BQU07WUFBSztJQUN6RCxJQUNBO0lBQ0VJLE1BQU07QUFDUjtBQUlKLGlFQUFlUCxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHlwZWFyZW5hLy4vc3JjL3N0b3JlL3VzZVN0b3JlLnRzPzhmZGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSAnenVzdGFuZCc7XG5pbXBvcnQgeyBwZXJzaXN0IH0gZnJvbSAnenVzdGFuZC9taWRkbGV3YXJlJztcblxuLy8gRGVmaW5lIHRoZSB0eXBlcyBmb3IgdGhlIGF1dGhlbnRpY2F0aW9uIHN0b3JlXG5pbnRlcmZhY2UgQXV0aFN0YXRlIHtcbiAgaXNBdXRoZW50aWNhdGVkOiBib29sZWFuO1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgbG9naW46ICh1c2VyRGF0YTogVXNlcikgPT4gdm9pZDtcbiAgbG9nb3V0OiAoKSA9PiB2b2lkO1xufVxuXG4vLyBEZWZpbmUgdGhlIHR5cGUgZm9yIHRoZSBVc2VyIChjdXN0b21pemUgYmFzZWQgb24geW91ciByZXF1aXJlbWVudHMpXG5pbnRlcmZhY2UgVXNlciB7XG4gIGlkPzogc3RyaW5nO1xuICBmdWxsbmFtZT86IHN0cmluZztcbiAgZW1haWw/OiBzdHJpbmc7XG4gIHRva2VuOiBzdHJpbmc7XG4gIC8vIEFkZCBtb3JlIGZpZWxkcyBpZiBuZWNlc3Nhcnlcbn1cblxuLy8gQ3JlYXRlIHRoZSB6dXN0YW5kIHN0b3JlIHdpdGggdHlwZSBhbm5vdGF0aW9ucyBhbmQgcGVyc2lzdCBtaWRkbGV3YXJlXG5jb25zdCB1c2VBdXRoU3RvcmUgPSBjcmVhdGUoXG4gIHBlcnNpc3Q8QXV0aFN0YXRlPihcbiAgICAoc2V0KSA9PiAoe1xuICAgICAgaXNBdXRoZW50aWNhdGVkOiBmYWxzZSxcbiAgICAgIHVzZXI6IG51bGwsXG4gICAgICBsb2dpbjogKHVzZXJEYXRhOiBVc2VyKSA9PiBzZXQoeyBpc0F1dGhlbnRpY2F0ZWQ6IHRydWUsIHVzZXI6IHVzZXJEYXRhIH0pLFxuICAgICAgbG9nb3V0OiAoKSA9PiBzZXQoeyBpc0F1dGhlbnRpY2F0ZWQ6IGZhbHNlLCB1c2VyOiBudWxsIH0pLFxuICAgIH0pLFxuICAgIHtcbiAgICAgIG5hbWU6ICdhdXRoLXN0b3JhZ2UnLCAvLyBVbmlxdWUgbmFtZSBmb3IgdGhlIHN0b3JlXG4gICAgfVxuICApXG4pO1xuXG5leHBvcnQgZGVmYXVsdCB1c2VBdXRoU3RvcmU7XG4iXSwibmFtZXMiOlsiY3JlYXRlIiwicGVyc2lzdCIsInVzZUF1dGhTdG9yZSIsInNldCIsImlzQXV0aGVudGljYXRlZCIsInVzZXIiLCJsb2dpbiIsInVzZXJEYXRhIiwibG9nb3V0IiwibmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/store/useStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e52554fe1e97\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHlwZWFyZW5hLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8yZTg0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTUyNTU0ZmUxZTk3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(pages)/playground/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/(pages)/playground/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\typing G\TypeArena\src\app\(pages)\playground\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistVF.woff\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistVF.woff\\\",\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistMonoVF.woff\",\"variable\":\"--font-geist-mono\",\"weight\":\"100 900\"}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistMonoVF.woff\\\",\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _vercel_analytics_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vercel/analytics/react */ \"(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_custom_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/Navbar */ \"(rsc)/./src/components/custom/Navbar.tsx\");\n/* harmony import */ var _components_custom_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom/Footer */ \"(rsc)/./src/components/custom/Footer.tsx\");\n/* harmony import */ var _components_theme_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/theme-providers */ \"(rsc)/./src/components/theme-providers.tsx\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"TypeArena\",\n    description: \"A typing test game\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    async: true,\n                    src: \"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6624328231679034\",\n                    crossOrigin: \"anonymous\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8___default().variable)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        src: \"https://checkout.razorpay.com/v1/checkout.js\",\n                        strategy: \"lazyOnload\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_providers__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n                        attribute: \"class\",\n                        defaultTheme: \"system\",\n                        enableSystem: true,\n                        disableTransitionOnChange: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_Navbar__WEBPACK_IMPORTED_MODULE_3__.Navbar, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 9\n                            }, this),\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_Footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vercel_analytics_react__WEBPACK_IMPORTED_MODULE_1__.Analytics, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/custom/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/custom/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-background border-t  text-foreground py-8 transition-colors duration-300\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-2xl font-bold text-yellow-500 mb-4\",\n                                    children: \"TypeArena\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Master Your Typing Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                    className: \"text-xl font-semibold text-gray-500 mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"hover:text-yellow-500 transition-colors\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                lineNumber: 16,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                            lineNumber: 16,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/faq\",\n                                                className: \"hover:text-yellow-500 transition-colors\",\n                                                children: \"FAQ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                lineNumber: 17,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                            lineNumber: 17,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/contact\",\n                                                className: \"hover:text-yellow-500 transition-colors\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                lineNumber: 18,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/terms\",\n                                                className: \"hover:text-yellow-500 transition-colors\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                    className: \"text-xl font-semibold text-gray-500 mb-4\",\n                                    children: \"Connect With Us\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://github.com/itzamanjain/typearena\",\n                                            className: \"text-muted-foreground hover:text-yellow-500 transition-colors\",\n                                            \"aria-label\": \"Facebook\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                \"aria-hidden\": \"true\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                    lineNumber: 27,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://github.com/itzamanjain/typearena\",\n                                            className: \"text-muted-foreground hover:text-yellow-500 transition-colors\",\n                                            \"aria-label\": \"Twitter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                \"aria-hidden\": \"true\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://github.com/itzamanjain/typearena\",\n                                            className: \"text-muted-foreground hover:text-yellow-500 transition-colors\",\n                                            \"aria-label\": \"GitHub\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" TypeArena. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2\",\n                            children: [\n                                \"Designed and Developed with ❤️ by\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://github.com/itzamanjain\",\n                                    className: \"text-yellow-500 hover:underline\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    children: \"Aman\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                \"&\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://github.com/adityaxjain\",\n                                    className: \"text-yellow-500 hover:underline\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    children: \"Aditya\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\typing G\\\\TypeArena\\\\src\\\\components\\\\custom\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/custom/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/custom/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/custom/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navbar: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\typing G\TypeArena\src\components\custom\Navbar.tsx#Navbar`);


/***/ }),

/***/ "(rsc)/./src/components/theme-providers.tsx":
/*!********************************************!*\
  !*** ./src/components/theme-providers.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\typing G\TypeArena\src\components\theme-providers.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90eXBlYXJlbmEvLi9zcmMvYXBwL2Zhdmljb24uaWNvPzI1ZjQiXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/@radix-ui","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@floating-ui","vendor-chunks/tailwind-merge","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/react-remove-scroll","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/aria-hidden","vendor-chunks/@vercel","vendor-chunks/next-themes","vendor-chunks/react-remove-scroll-bar","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/use-callback-ref","vendor-chunks/proxy-from-env","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/supports-color","vendor-chunks/react-style-singleton","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/get-nonce","vendor-chunks/react-hot-toast","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(pages)%2Fplayground%2Fpage&page=%2F(pages)%2Fplayground%2Fpage&appPaths=%2F(pages)%2Fplayground%2Fpage&pagePath=private-next-app-dir%2F(pages)%2Fplayground%2Fpage.tsx&appDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSarth%5CDownloads%5Ctyping%20G%5CTypeArena&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();